from UnleashClient import UnleashClient
import os
import requests
import json

def get_env_config(env):
    if env == "staging":
        return {
            "url": "https://unleash.staging.magie.services/api",
            "token": os.getenv("UNLEASH_STAGING_TOKEN"),
            "token_admin": os.getenv("UNLEASH_STAGING_TOKEN_ADMIN"),
            "project_id": "default",
            "environment": "development"
        }
    elif env == "prod":
        return {
            "url": "https://unleash.giro.services/api",
            "token": os.getenv("UNLEASH_PROD_TOKEN")
        }
    else:
        raise ValueError("Invalid environment")


ENV = "staging"
CONFIG = get_env_config(ENV)


url = CONFIG["url"]
project_id = CONFIG["project_id"]
token = CONFIG["token"]
token_admin = CONFIG["token_admin"]
environment = CONFIG["environment"]

client = UnleashClient(
        url=url,
        app_name="auth",
        # environment="development",
        # project_name="default",
        custom_headers={"Authorization": token}
    )
client.initialize_client()

def is_enabled(feature_name, key):
    response = client.is_enabled(feature_name, context={"userId": key})
    
    print(f'{feature_name}({key}): {response}')

    return response

def get_feature_flag_strategies(feature_name):
    headers = {
        'Accept': 'application/json',
        'Authorization': token_admin
    }

    response = requests.get(f"{url}/admin/projects/{project_id}/features/{feature_name}/environments/{environment}/strategies", headers=headers)
    
    print(response.json())

    return response

def get_feature_flag_strategy_by_name(feature_name, strategy_name):
    strategies = get_feature_flag_strategies(feature_name)
    strategy = [strategy for strategy in strategies.json() if strategy["name"] == strategy_name][0]
    return strategy

def manage_user_to_feature_flag(action, feature_name, user_id):
    strategy_user_with_id = get_feature_flag_strategy_by_name(feature_name, "userWithId")
    strategy_id = strategy_user_with_id["id"]

    print(strategy_user_with_id)
    
    if action == "add":
        user_ids = set(strategy_user_with_id["parameters"]["userIds"].split(","))
        if user_id in user_ids:
            return
        user_ids.add(user_id)
        new_user_ids = ",".join(user_ids)
    elif action == "remove":
        user_ids = set(strategy_user_with_id["parameters"]["userIds"].split(","))
        if user_id not in user_ids:
            return
        user_ids.remove(user_id)
        new_user_ids = ",".join(user_ids)
    else:
        raise ValueError("Invalid action")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': token_admin
    }

    data = {
        'parameters': {
            'userIds': new_user_ids
        }
    }
    
    print(data)

    response = requests.put(f"{url}/admin/projects/{project_id}/features/{feature_name}/environments/{environment}/strategies/{strategy_id}", headers=headers, data=json.dumps(data))
    
    print(response.json())

    return response


manage_user_to_feature_flag("add", "app_disable_mfa_sms", "adce9de6-6543-4693-afab-29ab7d5508d3")
manage_user_to_feature_flag("remove", "app_disable_mfa_sms", "adce9de6-6543-4693-afab-29ab7d5508d4")

is_enabled("app_disable_mfa_sms", "adce9de6-6543-4693-afab-29ab7d5508d1")
is_enabled("app_disable_mfa_sms", "adce9de6-6543-4693-afab-29ab7d5508dd")

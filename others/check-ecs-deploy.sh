#!/bin/bash

IMAGE_URI="-----Image URI-----"
SERVICE_NAME="gateway"
ECS_CLUSTER="prod-cluster-giro"

echo "Starting deployment verification for service $SERVICE_NAME-prod in cluster $ECS_CLUSTER"

LATEST_TASK_DEF=$(aws ecs list-task-definitions --family-prefix $SERVICE_NAME-prod --sort DESC --status ACTIVE --query "taskDefinitionArns[0]" --output text)
echo "New active task definition: $LATEST_TASK_DEF"

check_tasks() {
    echo "----------------------------------------------------------------------------------------------------"
    echo "$(date): Checking all running tasks..."

    TASKS=$(aws ecs list-tasks --cluster $ECS_CLUSTER --service-name $SERVICE_NAME-prod --output json)
    TASK_ARNS=$(echo $TASKS | jq -r '.taskArns[]')

    if [ -z "$TASK_ARNS" ]; then
        echo "No running tasks found."
        return 1
    fi

    ALL_TASKS_UPDATED=0

    for TASK_ARN in $TASK_ARNS; do
        TASK_INFO=$(aws ecs describe-tasks --cluster $ECS_CLUSTER --tasks $TASK_ARN --output json)

        TASK_DEF=$(echo $TASK_INFO | jq -r '.tasks[0].taskDefinitionArn')
        TASK_STATUS=$(echo $TASK_INFO | jq -r '.tasks[0].lastStatus')

        RUNNING_IMAGE=$(echo $TASK_INFO | jq -r --arg SERVICE_NAME "$SERVICE_NAME" '.tasks[0].containers[] | select(.name==$SERVICE_NAME + "-prod").image')

        echo "Task: $TASK_ARN"
        # echo "  - Status: $TASK_STATUS"
        # echo "  - Task Definition: $TASK_DEF"
        # echo "  - Image: $RUNNING_IMAGE"

        if [ "arn:aws:ecs:us-east-1:836929571495:task-definition/gateway-prod:30" != "$LATEST_TASK_DEF" ]; then
            # echo "  - Not running the latest task definition"
            ALL_TASKS_UPDATED=1
        else
            echo "  - Running the latest task definition ✓"
        fi
    done

    # Return 0 if all tasks are updated, 1 otherwise
    return $ALL_TASKS_UPDATED
}

TIMEOUT=900  # 15 minutes timeout
START_TIME=$(date +%s)

while true; do
    if check_tasks; then
        echo "All tasks are now running the expected image: $IMAGE_URI"
        exit 0
    fi

    CURRENT_TIME=$(date +%s)
    ELAPSED_TIME=$((CURRENT_TIME - START_TIME))

    if [ $ELAPSED_TIME -gt $TIMEOUT ]; then
        echo "Timeout reached. Not all tasks are running the expected image."
        exit 1
    fi

    sleep 15
done
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>AWS WAF CAPTCHA Test</title>
  <!-- Script do AWS WAF CAPTCHA – substitua INTEGRATION_URL pelo URL do script -->
  <script type="text/javascript" src="https://afe06c218561.us-east-1.captcha-sdk.awswaf.com/afe06c218561/jsapi.js" defer></script>
</head>
<body>
  <h1>Teste AWS WAF CAPTCHA</h1>
  <!-- Container para exibir o CAPTCHA -->
  <div id="captcha-container"></div>
  <!-- <PERSON><PERSON><PERSON> que só aparece após CAPTCHA resolvido -->
  <button id="fetch-btn" style="display:none">Fazer request protegida</button>
  <!-- Container para mostrar resposta da requisição -->
  <pre id="output"></pre>

  <script>
    // 1. <PERSON>uando clicar, renderizamos o CAPTCHA
    document.getElementById('captcha-container').addEventListener('click', function() {
      AwsWafCaptcha.renderCaptcha(this, {
        apiKey: "eQZKxtj5+qfV8xdETVkI6mFF3fYQPVYXp5Btkrtz62y0bUSJ/g2rTiQNtvtyEUrACAOWHeaO7+UFk70sbM9DD4TeiNOVIXXJ48J59pRjWziq9fWLgHhI/88257EF9dC2HtycPXCNAry1CHz0Gg0ELtm0IUT63aiJDftMm2owt7JO9FeXkeF5FNEh+40LljuFxkKeLbZOWDZAl0xmpuCtI0nX618XO//EuGsbPK3puZ4XBSf/7o9IRn1li2IfEf3uEaloFQz1ZIsP4JfFH6oVGfze/mzYWT04R1dqyTo2ePFcExZZs2bFY5B214ZwvKoX4O2rfsYbuh7Bpq8f8QbAw08cCkqkmEzsWQpCQU3pIw/QCbspprfGEBp4ewC6aj6x2Roy9E8h5ZRHG3zmSP/8tZIuMCpttfx9KRLXIjvSpN1revImU7iqKVgICLiwtFxLf3aDFdy7FPmO6J3LtnmRepvR1Vm8LYNx2LHiXTm5raYAGWxftuUuoXzBBEXMLb2oMRhvlqQB9v/cnMZdwzl+rYvCaNUuJVehyeQYAUzKi0lMkkCDY5lw2yNJeuR1Z6WFKJUnA1X6f+YsHC+ligqQmeL1fem1ZmLs8q6sRvT6u6qNLtj/Ud2kVB1Vkmo/6ZjLYxfB4pNaT9U8lK3QGAS2yFyJxBzDXkNqTnraeOnGcCI=_0_1", // substitua pela sua API key AWS WAF
        onSuccess: function(wafToken) {
          console.log('CAPTCHA resolvido:', wafToken);
          document.getElementById('fetch-btn').style.display = 'block';
        },
        onError: function(err) {
          console.error('Erro no CAPTCHA', err);
        }
      });
    });

    // 2. Ao clicar no botão, fazemos request usando o fetch wrapper do SDK
    document.getElementById('fetch-btn').addEventListener('click', async function() {
      try {
        const res = await AwsWafIntegration.fetch('/protected-endpoint', { method: 'GET' });
        const data = await res.text();
        document.getElementById('output').textContent = data;
      } catch (e) {
        document.getElementById('output').textContent = `Erro: ${e}`;
      }
    });
  </script>
</body>
</html>
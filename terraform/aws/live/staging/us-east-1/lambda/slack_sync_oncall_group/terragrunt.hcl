include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../modules/lambda"
}

inputs = {
  function_name = "slack_sync_oncall_group_2"
  description   = "Lambda function that syncs the on-call group in Slack and Github"
  runtime       = "python3.13"
  handler       = "main.lambda_handler"
  timeout       = 20
  memory_size   = 128
  source_path   = "./src"
  environment   = "staging"
  # vpc_id        = "vpc-0c81cfcee3629b43a"

  enable_eventbridge_schedule = true
  schedule_expression         = "cron(* * * * ? *)"
  enable_secrets_manager      = true

  tags = {
    product = "internal_tool"
    team    = "foundation"
  }
}

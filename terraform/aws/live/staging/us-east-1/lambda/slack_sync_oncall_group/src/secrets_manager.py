import json


def get_all(secrets_manager_client, secret_id):
    try:
        secret_value = secrets_manager_client.get_secret_value(SecretId=secret_id)
        secret_string = secret_value.get("SecretString")
        if not secret_string:
            raise ValueError(f"Secret '{secret_id}' has no SecretString value")
        return json.loads(secret_string)
    except secrets_manager_client.exceptions.ResourceNotFoundException:
        raise ValueError(f"Secret '{secret_id}' not found")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in secret '{secret_id}': {e}")
    except Exception as e:
        raise RuntimeError(f"Failed to retrieve secret '{secret_id}': {e}")

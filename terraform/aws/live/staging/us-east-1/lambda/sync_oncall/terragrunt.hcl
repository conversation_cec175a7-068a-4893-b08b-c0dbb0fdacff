include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../modules/lambda"
}

inputs = {
  function_name = "sync_oncall"
  description   = "Lambda function that syncs the Datadog on-call schedule with <PERSON><PERSON>ck and Gith<PERSON>"
  runtime       = "python3.13"
  handler       = "main.lambda_handler"
  timeout       = 20
  memory_size   = 128
  source_path   = "./src"
  environment   = "staging"

  enable_eventbridge_schedule = true
  schedule_expression         = "cron(* * * * ? *)"
  enable_secrets_manager      = true

  tags = {
    product = "internal_tool"
    team    = "foundation"
  }
}

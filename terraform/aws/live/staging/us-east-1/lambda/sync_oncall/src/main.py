import urllib3
import tempfile
import os
import datetime
import re
import json
import boto3
from secrets_manager import get_all

secrets_manager_client = boto3.client("secretsmanager", region_name="us-east-1")
SECRETS = get_all(secrets_manager_client, "staging/sync_oncall_lambda/secrets")

# Configuration
ONCALL_SCHEDULE_URL = SECRETS['ONCALL_SCHEDULE_URL']
SLACK_BOT_TOKEN = SECRETS['SLACK_BOT_TOKEN']
SLACK_USERGROUP_ID = SECRETS['SLACK_USERGROUP_ID']

GITHUB_TOKEN = SECRETS['GITHUB_TOKEN']
GITHUB_TOKEN_OWNER_USERNAME = SECRETS['GITHUB_TOKEN_OWNER_USERNAME']
GITHUB_TEAM_SLUG = SECRETS['GITHUB_TEAM_SLUG']

# Constants
CACHE_FILE = "/tmp/current-oncall-cached"
GITHUB_API_URL = "https://api.github.com"
GITHUB_ORG_NAME = "GiroOfficial"

http = urllib3.PoolManager()
GITHUB_HEADERS = {
    "Authorization": f"Bearer {GITHUB_TOKEN}",
    "Accept": "application/vnd.github+json",
    "User-Agent": "lambda-github-manager"
}

# TODO: what's the best place to keep this list?
ONCALL_ID_TO_GITHUB_USERNAME = {
    "<EMAIL>": "mrzkbruno",
    "<EMAIL>": "danilomagie",
    "<EMAIL>": "fabiano-magie",
    "<EMAIL>": "flplvr-magie",
    "<EMAIL>": "pepe-matt",
    "<EMAIL>": "igor-giro",
    "<EMAIL>": "jacksondouglasti",
    "<EMAIL>": "jvtnascimento",
    "<EMAIL>": "julio-cesar-giro",
    "<EMAIL>": "Levis44",
    "<EMAIL>": "xrhd",
    "<EMAIL>": "rodrigobarreiros",
    "<EMAIL>": "rodolfoizidoro",
    "<EMAIL>": "wennder-magie",
}

def download_ics_file(url):
    print("Downloading .ics file")
    response = http.request("GET", url)
    if response.status != 200:
        print(f"Failed to download .ics file: {response.status}")
        return None

    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_file.write(response.data)
    temp_file.close()
    return temp_file.name

def parse_ics_file(file_path):
    now = datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%SZ")
    with open(file_path, 'r', encoding='utf-8') as f:
        inside = False
        dtstart = dtend = summary = ""
        for line in f:
            line = line.strip()
            if line.startswith("BEGIN:VEVENT"):
                inside = True
            elif line.startswith("END:VEVENT"):
                inside = False
                if dtstart <= now <= dtend:
                    return summary
                dtstart = dtend = summary = ""
            elif inside:
                if line.startswith("DTSTART"):
                    dtstart = line.split(":")[1]
                elif line.startswith("DTEND"):
                    dtend = line.split(":")[1]
                elif line.startswith("SUMMARY"):
                    summary = line.split(":", 1)[1]
    return None

def extract_email(summary):
    match = re.search(r'[\[\(]([^\]\)]+)[\]\)]', summary)
    return match.group(1) if match else None

def read_cached_email():
    if os.path.exists(CACHE_FILE):
        with open(CACHE_FILE, 'r') as f:
            return f.read().strip()
    return ""

def write_cached_email(email):
    with open(CACHE_FILE, 'w') as f:
        f.write(email)

# === GitHub API Helper ===
def github_request(method, url, body=None, expect_json=True, allow_204=False):
    encoded_body = json.dumps(body).encode('utf-8') if body else None
    response = http.request(method, url, headers=GITHUB_HEADERS, body=encoded_body)
    status = response.status
    data = response.data.decode()

    if not allow_204 and status == 204:
        raise Exception(f"Unexpected 204 No Content: {method} {url}")

    if status >= 400:
        raise Exception(
            f"GitHub API error {status}:\nURL: {url}\nMethod: {method}\nBody: {body}\nResponse: {data}"
        )

    print(f"GitHub API response: {response}")
    return json.loads(data) if expect_json else data

def lookup_slack_user_id(email):
    print(f"Looking up Slack user ID for {email}")
    url = f"https://slack.com/api/users.lookupByEmail?email={email}"
    headers = {"Authorization": f"Bearer {SLACK_BOT_TOKEN}"}
    response = http.request("GET", url, headers=headers)
    data = json.loads(response.data.decode("utf-8"))
    return data.get("user", {}).get("id")

def update_slack_usergroup(user_id):
    print(f"Updating Slack usergroup with user ID: {user_id}")
    url = "https://slack.com/api/usergroups.users.update"
    headers = {
        "Authorization": f"Bearer {SLACK_BOT_TOKEN}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    fields = {
        "usergroup": SLACK_USERGROUP_ID,
        "users": user_id
    }
    encoded_fields = urllib3.request.urlencode(fields)
    response = http.request("POST", url, headers=headers, body=encoded_fields)
    data = json.loads(response.data.decode("utf-8"))
    if not data.get("ok"):
        print(f"Error updating usergroup: {data.get('error')}")
    else:
        print(f"Successfully updated on-call user to Slack user ID: {user_id}")

def update_github_oncall_team_members(target_user):
    # Add target user first to ensure access isn't lost
    add_url = f"{GITHUB_API_URL}/orgs/{GITHUB_ORG_NAME}/teams/{GITHUB_TEAM_SLUG}/memberships/{target_user}"
    github_request("PUT", add_url, body={"role": "member"})
    print(f"Added {target_user} to github on-call")

    # Fetch current members
    members_url = f"{GITHUB_API_URL}/orgs/{GITHUB_ORG_NAME}/teams/{GITHUB_TEAM_SLUG}/members"
    members = github_request("GET", members_url)
    current_logins = [m["login"] for m in members]
    print(f"Current members in github on-call: {current_logins}")

    # Remove everyone else
    for user in current_logins:
        if user != target_user and user != GITHUB_TOKEN_OWNER_USERNAME:
            del_url = f"{GITHUB_API_URL}/orgs/{GITHUB_ORG_NAME}/teams/{GITHUB_TEAM_SLUG}/memberships/{user}"
            github_request("DELETE", del_url, allow_204=True)

def lambda_handler(event=None, context=None):
    print("Starting on-call updater")

    ics_path = download_ics_file(ONCALL_SCHEDULE_URL)
    if not ics_path:
        return {"status": "error", "message": "Failed to download .ics file"}

    current_event = parse_ics_file(ics_path)
    os.remove(ics_path)

    if not current_event:
        print("No current on-call event found.")
        return {"status": "ok", "message": "No on-call event"}

    oncall_user_email = extract_email(current_event)
    if not oncall_user_email:
        print("Could not extract email from event summary.")
        return {"status": "error", "message": "Failed to extract email"}

    print(f"Current on-call from schedule: {oncall_user_email}")

    cached_event = read_cached_email()
    if oncall_user_email == cached_event:
        print("On-call user has not changed.")
        return {"status": "ok", "message": "No change"}

    slack_user_id = lookup_slack_user_id(oncall_user_email)
    if not slack_user_id:
        print(f"Could not find Slack user ID for: {oncall_user_email}")
        return {"status": "error", "message": "Slack user ID not found"}

    update_slack_usergroup(slack_user_id)

    github_target_user = ONCALL_ID_TO_GITHUB_USERNAME.get(oncall_user_email)
    print(f"Github user: {github_target_user}")
    if not github_target_user:
        return {"statusCode": 404, "body": json.dumps({"error": f"No GitHub user mapped for email: {oncall_user_email}"})}

    update_github_oncall_team_members(github_target_user)

    write_cached_email(oncall_user_email)

    return {"status": "ok", "message": f"Updated Slack usergroup with {oncall_user_email}. Updated Github on-call team with {github_target_user}"}
